PODS:
  - Alamofire (5.9.1)
  - AppAuth (1.7.5):
    - AppAuth/Core (= 1.7.5)
    - AppAuth/ExternalUserAgent (= 1.7.5)
  - AppAuth/Core (1.7.5)
  - AppAuth/ExternalUserAgent (1.7.5):
    - AppAuth/Core
  - DGCharts (5.1.0):
    - DGCharts/Core (= 5.1.0)
  - DGCharts/Core (5.1.0)
  - FBAEMKit (17.0.3):
    - FBSDKCoreKit_Basics (= 17.0.3)
  - FBSDKCoreKit (17.0.3):
    - FBAEMKit (= 17.0.3)
    - FBSDKCoreKit_Basics (= 17.0.3)
  - FBSDKCoreKit_Basics (17.0.3)
  - FBSDKLoginKit (17.0.3):
    - FBSDKCoreKit (= 17.0.3)
  - Firebase (11.2.0):
    - Firebase/Core (= 11.2.0)
  - Firebase/Core (11.2.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.2.0)
  - Firebase/CoreOnly (11.2.0):
    - FirebaseCore (= 11.2.0)
  - FirebaseAnalytics (11.2.0):
    - FirebaseAnalytics/AdIdSupport (= 11.2.0)
    - FirebaseCore (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.2.0):
    - FirebaseCore (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.2.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAppCheckInterop (11.3.0)
  - FirebaseAuth (11.3.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.0)
    - FirebaseCoreExtension (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GTMSessionFetcher/Core (~> 3.4)
    - RecaptchaInterop (~> 100.0)
  - FirebaseAuthInterop (11.3.0)
  - FirebaseCore (11.2.0):
    - FirebaseCoreInternal (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.3.0):
    - FirebaseCore (~> 11.0)
  - FirebaseCoreInternal (11.2.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseInstallations (11.2.0):
    - FirebaseCore (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.2.0):
    - FirebaseCore (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - FMDB (2.7.11):
    - FMDB/standard (= 2.7.11)
  - FMDB/standard (2.7.11)
  - GoogleAppMeasurement (11.2.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.2.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.2.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.2.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.2.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleSignIn (7.1.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities/AppDelegateSwizzler (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.0.2):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.0.2):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.0.2)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.0.2)
  - GoogleUtilities/Reachability (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher/Core (3.5.0)
  - HandyJSON (5.0.2)
  - IQKeyboardManagerSwift (7.0.3)
  - JTAppleCalendar (8.0.5)
  - Kingfisher (8.1.0)
  - MJRefresh (3.7.9)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - ObjectMapper (4.4.2)
  - PromisesObjC (2.4.0)
  - RecaptchaInterop (100.0.0)
  - SDWebImage (5.19.2):
    - SDWebImage/Core (= 5.19.2)
  - SDWebImage/Core (5.19.2)
  - SnapKit (5.7.1)
  - SwiftyJSON (5.0.2)

DEPENDENCIES:
  - Alamofire
  - DGCharts
  - FBSDKLoginKit
  - Firebase
  - FirebaseAuth
  - FirebaseCore
  - FirebaseMessaging
  - FMDB
  - GoogleSignIn
  - HandyJSON
  - IQKeyboardManagerSwift
  - JTAppleCalendar
  - Kingfisher
  - MJRefresh
  - ObjectMapper
  - SDWebImage
  - SnapKit
  - SwiftyJSON

SPEC REPOS:
  trunk:
    - Alamofire
    - AppAuth
    - DGCharts
    - FBAEMKit
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FBSDKLoginKit
    - Firebase
    - FirebaseAnalytics
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - FMDB
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - HandyJSON
    - IQKeyboardManagerSwift
    - JTAppleCalendar
    - Kingfisher
    - MJRefresh
    - nanopb
    - ObjectMapper
    - PromisesObjC
    - RecaptchaInterop
    - SDWebImage
    - SnapKit
    - SwiftyJSON

SPEC CHECKSUMS:
  Alamofire: f36a35757af4587d8e4f4bfa223ad10be2422b8c
  AppAuth: 501c04eda8a8d11f179dbe8637b7a91bb7e5d2fa
  DGCharts: 1c6daf585b6cfc78807af44ea690d357c410627f
  FBAEMKit: 9900b2edd99a2d21629a6277e6166f14c6215799
  FBSDKCoreKit: 0791f8f68a8630931a4c12aa23a56cc021551596
  FBSDKCoreKit_Basics: 46d6b472c0dd0a5a7e972c025033d1c567f54eb4
  FBSDKLoginKit: b4a4eba1d62eb452544411824f41689adabd5bd2
  Firebase: 98e6bf5278170668a7983e12971a66b2cd57fc8c
  FirebaseAnalytics: c36efd5710c60c17558650fa58c2066eca7e9265
  FirebaseAppCheckInterop: 7789a8adfb09e905ce02a76540b94b059029ea81
  FirebaseAuth: c7b82c8f3942c22629145c3f2972c33d1dc3ee6c
  FirebaseAuthInterop: c453b7ba7c49b88b2f519bb8d2e29edf7ada4a2a
  FirebaseCore: a282032ae9295c795714ded2ec9c522fc237f8da
  FirebaseCoreExtension: 30bb063476ef66cd46925243d64ad8b2c8ac3264
  FirebaseCoreInternal: 0c569513412da9f3b31bd0b340013bbee8f295c5
  FirebaseInstallations: 771177d89d6c451dc6e50085ec82e2fc77ed0a4a
  FirebaseMessaging: c9ec7b90c399c7a6100297e9d16f8a27fc7f7152
  FMDB: 57486c1117fd8e0e6b947b2f54c3f42bf8e57a4e
  GoogleAppMeasurement: 76d4f8b36b03bd8381fa9a7fe2cc7f99c0a2e93a
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleSignIn: d4281ab6cf21542b1cfaff85c191f230b399d2db
  GoogleUtilities: 26a3abef001b6533cf678d3eb38fd3f614b7872d
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  HandyJSON: 9e4e236f5d2dbefad5155a77417bbea438201c03
  IQKeyboardManagerSwift: f9c5dc36cba16ddd2e51fa7d51c34a2e083029b5
  JTAppleCalendar: 16c6501b22cb27520372c28b0a2e0b12c8d0cd73
  Kingfisher: dfbf20b6249ed4ef6f18d6a96c847bce860bdb95
  MJRefresh: ff9e531227924c84ce459338414550a05d2aea78
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  ObjectMapper: e6e4d91ff7f2861df7aecc536c92d8363f4c9677
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RecaptchaInterop: 7d1a4a01a6b2cb1610a47ef3f85f0c411434cb21
  SDWebImage: dfe95b2466a9823cf9f0c6d01217c06550d7b29a
  SnapKit: d612e99e678a2d3b95bf60b0705ed0a35c03484a
  SwiftyJSON: f5b1bf1cd8dd53cd25887ac0eabcfd92301c6a5a

PODFILE CHECKSUM: 8dfa61394b59b0e2a5b16521e467f2f3e2cbd6d5

COCOAPODS: 1.16.2
