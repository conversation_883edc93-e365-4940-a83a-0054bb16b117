import UIKit

class ScanView: UIView {
    
//    static var scanViewWidth = UIScreen.main.bounds.width - 40
    
    lazy var scanAnimationImage = UIImage()
    public var borderColor = UIColor.white
    public lazy var borderLineWidth: CGFloat = 0.4
    public lazy var cornerColor = UIColor.rgba(229, 194, 219)
    public lazy var cornerWidth: CGFloat = 2.0
    public lazy var backgroundAlpha: CGFloat = 0
    public lazy var scanBorderWidthRadio: CGFloat = 1
    lazy var scanBorderWidth = scanBorderWidthRadio * UIScreen.main.bounds.width
    lazy var scanBorderHeight = scanBorderWidth * 0.5
    private var scanViewSize: CGSize = CGSize(width: UIScreen.main.bounds.width - 40, height: 30)
    lazy var contentView = UIView(frame:  .zero)

    private lazy var imageView: UIImageView = {
        let imageView = UIImageView(image:  self.scanAnimationImage)
        return imageView
    }()

    override public init(frame:  CGRect) {
        super.init(frame:  frame)
        backgroundColor = .white
        contentView.backgroundColor = .clear
//        contentView.backgroundColor = .red
        contentView.clipsToBounds = true
        addSubview(contentView)
        contentView.snp.makeConstraints { make in
//            make.edges.equalToSuperview()
            make.left.right.top.bottom.equalToSuperview()
        }
        contentView.borderWidth = 1
        contentView.layer.borderColor = UIColor.clear.cgColor //UIColor.rgba(229, 165, 165).cgColor
    }

    required init?(coder aDecoder:  NSCoder) {
        fatalError("init(coder: ) has not been implemented")
    }

    override public func draw(_ rect:  CGRect) {
        super.draw(rect)
        drawScan(rect)
    }

    func startAnimation() {
        let rect = CGRect(x:  0, y:  0, width:  scanBorderWidth, height: scanBorderHeight)
        ScanAnimation.shared.startWith(rect, contentView, imageView:  imageView)
    }
    
    func stopAnimation() {
        ScanAnimation.shared.stopStepAnimating()
    }
    
    func refreshScanViewFrame(width: CGFloat) {
        
//        self.scanViewSize = CGSize(width: width, height: 30)
//        print("")
        self.layoutIfNeeded()
    }
}

extension ScanView {
    //画四个角还要调整
    func drawScan(_ rect:  CGRect) {
        UIColor.black.withAlphaComponent(backgroundAlpha).setFill()
        UIRectFill(rect)
    }
}

// MARK:  -  ScanAnimation
class ScanAnimation: NSObject{
    static let shared: ScanAnimation = {
        let instance = ScanAnimation()
        return instance
    }()
    lazy var animationImageView = UIImageView()
    var tempFrame: CGRect?
    var isAnimationing = false

    func startWith(_ rect: CGRect, _ parentView: UIView, imageView: UIImageView) {
        tempFrame = rect
        imageView.frame = tempFrame ?? CGRect.zero
        animationImageView = imageView
        animationImageView.alpha = 0.5
        parentView.addSubview(imageView)
        isAnimationing = true
        if imageView.image != nil {
            animation()
        }
    }

    @objc func animation() {
        guard isAnimationing else {
            return
        }
        var frame = tempFrame
        let hImg = animationImageView.image!.size.height * frame!.size.width / animationImageView.image!.size.width
        frame?.origin.y -= hImg
        frame?.size.height = hImg
        self.animationImageView.frame = frame ?? CGRect.zero

        UIView.animate(withDuration: 3, delay: 0, options: .curveLinear, animations: {
            var frame = self.tempFrame!
            let hImg = self.animationImageView.frame.size.height * self.tempFrame!.size.width / self.animationImageView.frame.size.width
            frame.origin.y += (frame.size.height - hImg)
            frame.size.height = hImg
            self.animationImageView.frame = frame
        }, completion: { _ in
            self.perform(#selector(ScanAnimation.animation), with: nil, afterDelay: 0.3)
        })
    }

    func stopStepAnimating() {
        self.animationImageView.alpha = 0
        isAnimationing = false
    }

    deinit {
        stopStepAnimating()
    }
}
