<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21679"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="RecordTestsViewController" customModule="HormoneLife" customModuleProvider="target">
            <connections>
                <outlet property="chooseFromGalleryButton" destination="BgK-0u-S7d" id="4rM-gx-0N6"/>
                <outlet property="scanbgView" destination="2jE-Zr-M3w" id="LE3-D1-NQ3"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleAspectFit" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Please align with the C line and the Max line." textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9GL-22-8tM">
                    <rect key="frame" x="50.333333333333343" y="99" width="292.66666666666663" height="20"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="20" id="Os9-hq-qxe"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="g3C-cH-jAC">
                    <rect key="frame" x="20" y="173" width="353" height="40"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ZB6-gY-drL">
                            <rect key="frame" x="78.666666666666671" y="0.0" width="4" height="40"/>
                            <color key="backgroundColor" red="0.87402927880000003" green="0.90140950679999998" blue="0.92936396600000004" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="4" id="ELz-eM-STg"/>
                                <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="4" id="evc-ZL-KoZ"/>
                            </constraints>
                        </view>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="4Ve-IJ-xrr">
                            <rect key="frame" x="178.66666666666666" y="0.0" width="4" height="40"/>
                            <color key="backgroundColor" red="0.85906678439999995" green="0.65510600809999997" blue="0.65098381039999997" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="4" id="RT8-WH-4yP"/>
                            </constraints>
                        </view>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="cfL-q3-2o4">
                            <rect key="frame" x="203.66666666666666" y="0.0" width="4" height="40"/>
                            <color key="backgroundColor" red="0.85005241629999995" green="0.4437052608" blue="0.43138396740000001" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                            <constraints>
                                <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="4" id="0h1-yZ-pFd"/>
                                <constraint firstAttribute="width" constant="4" id="Rdx-ST-0NI"/>
                            </constraints>
                        </view>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3XY-W8-lRu">
                            <rect key="frame" x="228.66666666666666" y="0.0" width="124.33333333333334" height="40"/>
                            <color key="backgroundColor" red="0.91926342250000004" green="0.85086673499999999" blue="0.85880857710000003" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                        </view>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="maxTitleImage" translatesAutoresizingMaskIntoConstraints="NO" id="xs4-Ih-RDE">
                            <rect key="frame" x="94.666666666666671" y="4" width="32" height="32"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="32" id="Uwt-bq-qx0"/>
                                <constraint firstAttribute="height" constant="32" id="gYK-WF-khj"/>
                            </constraints>
                        </imageView>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="pinkBoarderIcon" translatesAutoresizingMaskIntoConstraints="NO" id="fiX-Kv-hKp">
                            <rect key="frame" x="157.66666666666666" y="-6" width="92" height="52"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="92" id="7CL-xj-LcW"/>
                                <constraint firstAttribute="height" constant="52" id="qw2-PG-UyC"/>
                            </constraints>
                        </imageView>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="center" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Max" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="N2r-I4-v38">
                            <rect key="frame" x="59.666666666666671" y="-34" width="42" height="21"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="21" id="EFu-Ao-6xb"/>
                                <constraint firstAttribute="width" constant="42" id="mVY-Fv-cXZ"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="12"/>
                            <color key="textColor" red="0.077407650650000007" green="0.19995591039999999" blue="0.20000121000000001" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="center" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="T" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="0Ru-fp-xc1">
                            <rect key="frame" x="159.66666666666666" y="-34" width="42" height="21"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="42" id="mb2-O2-vJc"/>
                                <constraint firstAttribute="height" constant="21" id="rdK-hY-ewf"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="12"/>
                            <color key="textColor" red="0.077407650650000007" green="0.19995591039999999" blue="0.20000121000000001" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="center" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="C" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2EA-wu-ktB">
                            <rect key="frame" x="184.66666666666666" y="-34" width="42" height="21"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="21" id="gfK-x4-GuX"/>
                                <constraint firstAttribute="width" constant="42" id="gju-LB-U1J"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="12"/>
                            <color key="textColor" red="0.077407650650000007" green="0.19995591039999999" blue="0.20000121000000001" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="fiX-Kv-hKp" firstAttribute="centerY" secondItem="g3C-cH-jAC" secondAttribute="centerY" id="28c-0S-jiZ"/>
                        <constraint firstItem="2EA-wu-ktB" firstAttribute="centerX" secondItem="cfL-q3-2o4" secondAttribute="centerX" id="2u4-oX-eqr"/>
                        <constraint firstAttribute="bottom" secondItem="4Ve-IJ-xrr" secondAttribute="bottom" id="ATL-3t-fVV"/>
                        <constraint firstItem="4Ve-IJ-xrr" firstAttribute="top" secondItem="g3C-cH-jAC" secondAttribute="top" id="Fre-66-PuW"/>
                        <constraint firstItem="3XY-W8-lRu" firstAttribute="leading" secondItem="cfL-q3-2o4" secondAttribute="trailing" constant="21" id="HPK-ux-T27"/>
                        <constraint firstItem="3XY-W8-lRu" firstAttribute="top" secondItem="g3C-cH-jAC" secondAttribute="top" id="He9-u4-cSO"/>
                        <constraint firstItem="ZB6-gY-drL" firstAttribute="top" secondItem="g3C-cH-jAC" secondAttribute="top" id="O9C-0D-YNl"/>
                        <constraint firstItem="cfL-q3-2o4" firstAttribute="top" secondItem="g3C-cH-jAC" secondAttribute="top" id="PsH-KD-W5t"/>
                        <constraint firstAttribute="bottom" secondItem="cfL-q3-2o4" secondAttribute="bottom" id="Syk-kW-jIz"/>
                        <constraint firstItem="xs4-Ih-RDE" firstAttribute="centerY" secondItem="g3C-cH-jAC" secondAttribute="centerY" id="XHc-1I-Kju"/>
                        <constraint firstItem="2EA-wu-ktB" firstAttribute="top" secondItem="cfL-q3-2o4" secondAttribute="top" constant="-34" id="b80-cq-tup"/>
                        <constraint firstItem="4Ve-IJ-xrr" firstAttribute="leading" secondItem="ZB6-gY-drL" secondAttribute="trailing" constant="96" id="bf0-Ik-OXn"/>
                        <constraint firstItem="N2r-I4-v38" firstAttribute="top" secondItem="ZB6-gY-drL" secondAttribute="top" constant="-34" id="eYp-YB-7G3"/>
                        <constraint firstItem="N2r-I4-v38" firstAttribute="centerX" secondItem="ZB6-gY-drL" secondAttribute="centerX" id="fSy-g2-XaW"/>
                        <constraint firstItem="cfL-q3-2o4" firstAttribute="leading" secondItem="4Ve-IJ-xrr" secondAttribute="trailing" constant="21" id="hND-MG-I1b"/>
                        <constraint firstItem="4Ve-IJ-xrr" firstAttribute="centerX" secondItem="g3C-cH-jAC" secondAttribute="centerX" constant="4" id="hyH-4M-Cqu"/>
                        <constraint firstAttribute="height" constant="40" id="lkg-1a-nCC"/>
                        <constraint firstItem="0Ru-fp-xc1" firstAttribute="top" secondItem="4Ve-IJ-xrr" secondAttribute="top" constant="-34" id="qJM-1R-ELf"/>
                        <constraint firstItem="fiX-Kv-hKp" firstAttribute="centerX" secondItem="cfL-q3-2o4" secondAttribute="centerX" constant="-2" id="sMa-IR-Eh9"/>
                        <constraint firstAttribute="bottom" secondItem="ZB6-gY-drL" secondAttribute="bottom" id="tjS-ym-INI"/>
                        <constraint firstAttribute="trailing" secondItem="3XY-W8-lRu" secondAttribute="trailing" id="uTj-pe-qqJ"/>
                        <constraint firstItem="0Ru-fp-xc1" firstAttribute="centerX" secondItem="4Ve-IJ-xrr" secondAttribute="centerX" id="wRi-ol-adE"/>
                        <constraint firstAttribute="bottom" secondItem="3XY-W8-lRu" secondAttribute="bottom" id="zJb-02-DZC"/>
                        <constraint firstItem="xs4-Ih-RDE" firstAttribute="leading" secondItem="ZB6-gY-drL" secondAttribute="trailing" constant="12" id="zvg-ae-GNO"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="4"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </view>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="baseRedLine" translatesAutoresizingMaskIntoConstraints="NO" id="n3c-fG-2NH">
                    <rect key="frame" x="101.66666666666667" y="213" width="1" height="176"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="1" id="47y-Qk-1eO"/>
                    </constraints>
                </imageView>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="baseRedLine" translatesAutoresizingMaskIntoConstraints="NO" id="65v-V0-AbM" userLabel="baseTRedLine">
                    <rect key="frame" x="226.66666666666666" y="213" width="1" height="176"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="1" id="zuX-uW-jdw"/>
                    </constraints>
                </imageView>
                <view alpha="0.69999999999999996" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2jE-Zr-M3w" userLabel="bgview">
                    <rect key="frame" x="0.0" y="249" width="393" height="140"/>
                    <color key="backgroundColor" systemColor="labelColor"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="140" id="ao7-0J-DIU"/>
                    </constraints>
                </view>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="scanTip" highlightedImage="scanTip" translatesAutoresizingMaskIntoConstraints="NO" id="fsY-LA-rt4">
                    <rect key="frame" x="35" y="419" width="323" height="72"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="72" id="rH2-f6-V5E"/>
                    </constraints>
                </imageView>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="BgK-0u-S7d">
                    <rect key="frame" x="122.66666666666669" y="678" width="148" height="36"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="148" id="X3R-yl-jvJ"/>
                        <constraint firstAttribute="height" constant="36" id="kQP-WK-J5f"/>
                    </constraints>
                    <state key="normal" title="Button"/>
                    <buttonConfiguration key="configuration" style="plain">
                        <backgroundConfiguration key="background" cornerRadius="4"/>
                        <attributedString key="attributedTitle">
                            <fragment content="Choose from gallery">
                                <attributes>
                                    <font key="NSFont" size="13" name="Helvetica"/>
                                    <paragraphStyle key="NSParagraphStyle" alignment="center" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                </attributes>
                            </fragment>
                        </attributedString>
                        <color key="baseForegroundColor" red="0.015686274509803921" green="0.20392156862745098" blue="0.20392156862745098" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </buttonConfiguration>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="4"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.borderWidth">
                            <integer key="value" value="1"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="color" keyPath="layer.borderColor">
                            <color key="value" red="0.015686274509803921" green="0.20392156862745098" blue="0.20000000000000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <action selector="didTapChooseFromGallery:" destination="-1" eventType="touchUpInside" id="x4q-rx-QDD"/>
                    </connections>
                </button>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="0A3-4q-Nrj" userLabel="camera">
                    <rect key="frame" x="164.66666666666666" y="551" width="64" height="64"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="64" id="9gU-qG-TgS"/>
                        <constraint firstAttribute="width" constant="64" id="E82-S5-XSJ"/>
                    </constraints>
                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                    <state key="normal" image="scanCamera"/>
                    <connections>
                        <action selector="didTapCamera:" destination="-1" eventType="touchUpInside" id="V1B-ke-hyF"/>
                    </connections>
                </button>
            </subviews>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <color key="backgroundColor" red="0.9137254901960784" green="0.88627450980392153" blue="0.85098039215686272" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstItem="2jE-Zr-M3w" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="1Tw-4c-RPZ"/>
                <constraint firstItem="65v-V0-AbM" firstAttribute="bottom" secondItem="2jE-Zr-M3w" secondAttribute="bottom" id="1b2-ei-OXF"/>
                <constraint firstItem="fsY-LA-rt4" firstAttribute="top" secondItem="2jE-Zr-M3w" secondAttribute="bottom" constant="30" id="2dN-tL-ib6"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="g3C-cH-jAC" secondAttribute="trailing" constant="20" id="3Db-14-V6C"/>
                <constraint firstItem="fsY-LA-rt4" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="76z-kn-VVr"/>
                <constraint firstItem="9GL-22-8tM" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="Fki-42-weG"/>
                <constraint firstItem="g3C-cH-jAC" firstAttribute="top" secondItem="9GL-22-8tM" secondAttribute="bottom" constant="54" id="Isj-fY-gUY"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="2jE-Zr-M3w" secondAttribute="trailing" id="Q6W-Sv-d32"/>
                <constraint firstItem="n3c-fG-2NH" firstAttribute="trailing" secondItem="ZB6-gY-drL" secondAttribute="trailing" id="Vp1-Jh-pfY"/>
                <constraint firstItem="n3c-fG-2NH" firstAttribute="bottom" secondItem="2jE-Zr-M3w" secondAttribute="bottom" id="bdO-Fj-guP"/>
                <constraint firstItem="n3c-fG-2NH" firstAttribute="top" secondItem="g3C-cH-jAC" secondAttribute="top" constant="40" id="cMm-s9-8Ry"/>
                <constraint firstItem="fsY-LA-rt4" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" constant="35" id="dpI-jh-0wY"/>
                <constraint firstItem="0A3-4q-Nrj" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="ehB-7N-RXa"/>
                <constraint firstItem="0A3-4q-Nrj" firstAttribute="top" secondItem="fsY-LA-rt4" secondAttribute="bottom" constant="60" id="gAV-Tx-yH1"/>
                <constraint firstItem="65v-V0-AbM" firstAttribute="trailing" secondItem="cfL-q3-2o4" secondAttribute="trailing" id="gag-X0-AvT"/>
                <constraint firstItem="BgK-0u-S7d" firstAttribute="top" secondItem="0A3-4q-Nrj" secondAttribute="bottom" constant="63" id="hLx-sy-UF8"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="fsY-LA-rt4" secondAttribute="trailing" constant="35" id="iJ4-88-kSL"/>
                <constraint firstItem="65v-V0-AbM" firstAttribute="top" secondItem="g3C-cH-jAC" secondAttribute="top" constant="40" id="iem-KU-J7R"/>
                <constraint firstItem="2jE-Zr-M3w" firstAttribute="top" secondItem="g3C-cH-jAC" secondAttribute="bottom" constant="36" id="my3-ZK-ur7"/>
                <constraint firstItem="BgK-0u-S7d" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="o3T-JX-dus"/>
                <constraint firstItem="9GL-22-8tM" firstAttribute="top" secondItem="fnl-2z-Ty3" secondAttribute="top" constant="40" id="pzI-Vg-KSv"/>
                <constraint firstItem="g3C-cH-jAC" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" constant="20" id="ucQ-gK-Kji"/>
            </constraints>
            <point key="canvasLocation" x="-118.32061068702289" y="-163.38028169014086"/>
        </view>
    </objects>
    <resources>
        <image name="baseRedLine" width="1" height="176"/>
        <image name="maxTitleImage" width="31" height="32"/>
        <image name="pinkBoarderIcon" width="92" height="52"/>
        <image name="scanCamera" width="64" height="64"/>
        <image name="scanTip" width="300" height="71.333335876464844"/>
        <systemColor name="labelColor">
            <color red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
