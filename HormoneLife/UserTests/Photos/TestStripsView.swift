//
//  TestStripsView.swift
//  HormoneLife
//
//  Created by Tank on 2024/7/1.
//

import UIKit

class TestStripsView: UIView {
    
    let titleLa: UILabel = {
        let l = UILabel()
        l.font = .mediumGilroyFont(18)
        l.textColor = .white
        l.textAlignment = .center
        l.text = "Move the test strip to the viewfinder"
        return l
    }()
    
    let maxLabel: UILabel = {
        let l = UILabel()
        l.font = .regularGilroyFont(14)
        l.textColor = .white
        l.textAlignment = .center
        l.text = "Max"
        return l
    }()
    
    let tLabel: UILabel = {
        let l = UILabel()
        l.font = .regularGilroyFont(14)
        l.textColor = .white
        l.textAlignment = .center
        l.text = "T"
        return l
    }()
    
    let cLabel: UILabel = {
        let l = UILabel()
        l.font = .regularGilroyFont(14)
        l.textColor = .white
        l.textAlignment = .center
        l.text = "C"
        return l
    }()
    
    let whiteBGView: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor("#FFFFFF")
        return v
    }()
    
    let whiteGrayBGView: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor("#F3F3F3")
        return v
    }()
    
    let grayView: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor("#000000")
        return v
    }()
    
    let pinkView: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor("#DA95AA")
        return v
    }()
    
    let redView: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor("#9B1E24")
        return v
    }()
    
    let whitePinkView: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor("#DAB765")
        return v
    }()
    
    let maxImageView: UIImageView = {
        let i = UIImageView(image: UIImage(named: "maxTitleImage"))
        i.contentMode = .scaleAspectFit
        return i
    }()
    
    let lineBoarderImageView: UIImageView = {
        let i = UIImageView(image: UIImage(named: "pinkBoarderIcon"))
        i.contentMode = .scaleAspectFit
        i.isHidden = true
        return i
    }()
    
    let line1ImageView: UIImageView = {
        let i = UIImageView(image: UIImage(named: "baseRedLine"))
        i.contentMode = .scaleAspectFit
        return i
    }()
    
    let line2ImageView: UIImageView = {
        let i = UIImageView(image: UIImage(named: "baseRedLine"))
        i.contentMode = .scaleAspectFit
        return i
    }()
    
    let bgDarkVTop = UIView()
    let bgDarkV1 = UIView()
    let bgDarkV2 = UIView()
    let bgDarkV3 = UIView()
    let bgDarkV4 = UIView()
    let bgDarkVBottom = UIView()
    
    let cropView: UIView = {
        let v = UIView()
        v.backgroundColor = .clear
        return v
    }()
    
    let tipsLabel: UILabel = {
        let l = UILabel()
        l.font = .mediumGilroyFont(16)
        l.textColor = .white
        l.textAlignment = .center
        l.text = "Tips:\nPinch to zoom or rotate"
        l.numberOfLines = 0
        return l
    }()
    
    let zoomImageView: UIImageView = {
        let i = UIImageView(image: UIImage(named: "zoom"))
        i.contentMode = .scaleAspectFit
        return i
    }()
    
    let rotateImageView: UIImageView = {
        let i = UIImageView(image: UIImage(named: "rotate"))
        i.contentMode = .scaleAspectFit
        return i
    }()
    
    let zoomLabel: UILabel = {
        let l = UILabel()
        l.font = .regularGilroyFont(16)
        l.textColor = .white
        l.textAlignment = .center
        l.text = "Zoom"
        return l
    }()
    
    let rotateLabel: UILabel = {
        let l = UILabel()
        l.font = .regularGilroyFont(16)
        l.textColor = .white
        l.textAlignment = .center
        l.text = "Rotate"
        return l
    }()
    
    init(frame: CGRect, width : CGFloat) {
        super.init(frame: frame)
        setupUI(width: width)
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI(width: kScreenWidth - 40)
    }
    
    private func setupUI(width: CGFloat) {
        
        [bgDarkVTop, bgDarkVBottom, titleLa, maxLabel, tLabel, cLabel, whiteBGView, whiteGrayBGView, grayView, pinkView, redView, whitePinkView, maxImageView, lineBoarderImageView, line1ImageView, line2ImageView, bgDarkV1, bgDarkV2, bgDarkV3, bgDarkV4, tipsLabel, zoomImageView, rotateImageView, zoomLabel, rotateLabel, cropView].forEach(addSubview)
        
        let scanLeftMargin = ((kScreenWidth - width) / 2.0)
        let left158Margin = width * 0.158
        let left538Margin = width * 0.538
        
        titleLa.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(10)
            make.left.right.equalToSuperview().inset(20)
            make.height.equalTo(20)
        }
        
        whiteBGView.layer.cornerRadius = 5
        whiteBGView.layer.masksToBounds = true
        whiteBGView.snp.makeConstraints { make in
            make.height.equalTo(30)
            make.left.equalTo(20)
            make.right.equalTo(-20)
            make.top.equalTo(titleLa.snp.bottom).offset(64)
        }
        
        line1ImageView.snp.makeConstraints { make in
            make.width.equalTo(1)
            make.height.equalTo(186)
            make.top.equalTo(whiteBGView.snp.bottom)
            make.left.equalTo(scanLeftMargin + left158Margin + 3)
        }
        
        line2ImageView.snp.makeConstraints { make in
            make.width.equalTo(1)
            make.height.equalTo(186)
            make.top.equalTo(whiteBGView.snp.bottom)
            make.left.equalTo(scanLeftMargin + left538Margin + 3)
        }
        
        grayView.snp.makeConstraints { make in
            make.top.bottom.equalTo(whiteBGView)
            make.width.equalTo(4)
            make.centerX.equalTo(line1ImageView)
//            make.left.equalTo(scanLeftMargin + left158Margin - 1.5)
        }
        
        redView.snp.makeConstraints { make in
            make.width.equalTo(4)
            make.top.bottom.equalTo(whiteBGView)
            make.centerX.equalTo(line2ImageView)
//            make.left.equalTo(scanLeftMargin + left538Margin - 1.5)
        }
        
        pinkView.snp.makeConstraints { make in
            make.width.equalTo(4)
            make.top.bottom.equalTo(whiteBGView)
            make.right.equalTo(redView.snp.left).offset(-21)
//            make.centerX.equalToSuperview().offset(4)
        }
        
        whiteGrayBGView.snp.makeConstraints { make in
            make.height.left.bottom.equalTo(whiteBGView)
            make.right.equalTo(pinkView.snp.left).offset(-20)
        }
        
        whitePinkView.snp.makeConstraints { make in
            make.top.right.bottom.equalTo(whiteBGView)
            make.left.equalTo(redView.snp.right).offset(21)
        }
        
        lineBoarderImageView.snp.makeConstraints { make in
            make.width.equalTo(92)
            make.height.equalTo(52)
            make.centerY.equalTo(whiteBGView.snp.centerY)
            make.centerX.equalTo(redView.snp.centerX).offset(-2)
        }
        
        maxImageView.snp.makeConstraints { make in
            make.width.height.equalTo(32)
            make.centerY.equalTo(whiteBGView)
            make.left.equalTo(grayView.snp.right).offset(12)
        }
        
        maxLabel.snp.makeConstraints { make in
            make.bottom.equalTo(whiteBGView.snp.top).offset(-20)
            make.centerX.equalTo(grayView)
        }
        
        tLabel.snp.makeConstraints { make in
            make.top.equalTo(maxLabel)
            make.centerX.equalTo(pinkView)
        }
        
        cLabel.snp.makeConstraints { make in
            make.top.equalTo(maxLabel)
            make.centerX.equalTo(redView)
        }
        
        bgDarkV4.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.height.equalTo(24)
            make.bottom.equalTo(line1ImageView.snp.bottom)
        }
        
        let bgDarkV2V3Padding = (UIScreen.main.bounds.width - width) / 2
        bgDarkV2.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.width.equalTo(bgDarkV2V3Padding)
            make.height.equalTo(42)
            make.bottom.equalTo(bgDarkV4.snp.top)
        }
        
        bgDarkV3.snp.makeConstraints { make in
            make.right.equalToSuperview()
            make.width.equalTo(bgDarkV2V3Padding)
            make.height.equalTo(42)
            make.bottom.equalTo(bgDarkV4.snp.top)
        }
        
        bgDarkV1.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.height.equalTo(30)
            make.bottom.equalTo(bgDarkV2.snp.top)
        }
        [bgDarkV1, bgDarkV2, bgDarkV3, bgDarkV4].forEach {
            $0.backgroundColor = .rgba(0, 0, 0, 0.4)
        }
//        bgDarkV1.backgroundColor = .red
//        bgDarkV2.backgroundColor = .blue
//        bgDarkV3.backgroundColor = .yellow
//        bgDarkV4.backgroundColor = .black
        
        
        cropView.snp.makeConstraints { make in
            make.left.equalTo(bgDarkV2.snp.right)
            make.right.equalTo(bgDarkV3.snp.left)
            make.top.equalTo(bgDarkV1.snp.bottom)
            make.bottom.equalTo(bgDarkV4.snp.top)
        }
        
        bgDarkVTop.snp.makeConstraints { make in
            make.left.right.top.equalToSuperview()
            make.bottom.equalTo(bgDarkV1.snp.top)
        }
        
        bgDarkVBottom.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.top.equalTo(bgDarkV4.snp.bottom)
        }
        bgDarkVTop.backgroundColor = .rgba(0, 0, 0, 0.4)
        bgDarkVBottom.backgroundColor = .rgba(0, 0, 0, 0.4)
        
        tipsLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.greaterThanOrEqualTo(bgDarkV4.snp.bottom).offset(60)
        }
        
        zoomImageView.snp.makeConstraints { make in
            make.width.height.equalTo(48)
            make.top.equalTo(tipsLabel.snp.bottom).offset(40)
            make.centerX.equalToSuperview().offset(-40)
        }
        
        zoomLabel.snp.makeConstraints { make in
            make.top.equalTo(zoomImageView.snp.bottom).offset(8)
            make.centerX.equalTo(zoomImageView)
            make.bottom.equalToSuperview().inset(30)
        }
        
        rotateImageView.snp.makeConstraints { make in
            make.width.height.equalTo(48)
            make.top.equalTo(zoomImageView)
            make.centerX.equalToSuperview().offset(40)
        }
        
        rotateLabel.snp.makeConstraints { make in
            make.top.equalTo(rotateImageView.snp.bottom).offset(8)
            make.centerX.equalTo(rotateImageView)
        }
        
        self.bringSubviewToFront(line1ImageView)
        self.bringSubviewToFront(line2ImageView)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    
}
