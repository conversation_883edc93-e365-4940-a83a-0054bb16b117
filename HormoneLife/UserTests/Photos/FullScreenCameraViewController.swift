import UIKit
import AVFoundation

protocol FullScreenCameraViewControllerDelegate: AnyObject {
    func chooseFromGallery()
    func didCaptureImage(_ image: UIImage)
    func didReceiveValidationError()
}

// 自动拍照状态枚举
enum AutoCaptureState {
    case ready      // 准备开始自动拍照
    case running    // 正在自动拍照
    case stopped    // 已停止（手动停止或完成）
    
    var canAutoCapture: Bool {
        return self == .ready || self == .running
    }
}

class FullScreenCameraViewController: BaseViewController, ConfirmErrorPopupViewControllerDelegate {
    // MARK: - 常量定义
    private let kAutoStartDelay: TimeInterval = 4.0     // 自动开始延迟时间
    private var kMaxRetryAttempts: Int = 3             // 最大尝试次数（首次 + 2次重试）
    private let kRetryDelay: TimeInterval = 4.0        // 重试延迟时间，与首次一致
    
    // MARK: - 相机相关属性
    private var captureSession: AVCaptureSession?           // 相机会话
    private var previewLayer: AVCaptureVideoPreviewLayer?   // 预览图层
    private var videoDataOutput: AVCaptureVideoDataOutput?  // 视频数据输出
    private var currentVideoPixelBuffer: CVPixelBuffer?     // 当前视频帧缓冲
    
    // MARK: - 公共属性
    weak var delegate: FullScreenCameraViewControllerDelegate?
    weak var actionDelegate: FullScreenCameraViewControllerDelegate?
    var isAutoCatchImage: Bool = true  // 是否自动拍摄
    static var hasExecutedAutoCapture = false  // 标记是否已执行过自动拍照
    var retryCount: Int = 0            // 重试次数
    
    // MARK: - UI组件
    lazy var overlayView: OverlayView = {
        let overlayView = OverlayView.loadFromNib()
        overlayView.delegate = self
        return overlayView
    }()
    
    // MARK: - 生命周期方法
    override func viewDidLoad() {
        super.viewDidLoad()
        checkCameraPermission()
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        prepareForAutoCapture()
    }
    
    func prepareForAutoCapture() {
        overlayView.scanView.startAnimation()
        print("视图出现 - 当前状态: isAutoCatchImage=\(isAutoCatchImage), hasExecutedAutoCapture=\(FullScreenCameraViewController.hasExecutedAutoCapture)")

        // 首次进入：先倒计时，再开始自动拍照
        if isAutoCatchImage && !FullScreenCameraViewController.hasExecutedAutoCapture {
            FullScreenCameraViewController.hasExecutedAutoCapture = true
            overlayView.startCountdown(seconds: Int(kAutoStartDelay)) { [weak self] in
                self?.startAutoCapture(afterDelay: 0) // 倒计时结束立即执行
            }
        } else {
            print("不满足自动拍照条件，跳过自动拍照")
        }
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        print("视图即将消失，停止所有操作")
        overlayView.scanView.stopAnimation()
        stopCameraSession()
        stopAutoCapture()
    }
    
    deinit {
        print("FullScreenCameraViewController 被释放")
        stopCameraSession()
    }
    
    // MARK: - 相机权限检查
    private func checkCameraPermission() {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized:
            setupCamera()
            setupUI()
            // 在 viewDidAppear -> prepareForAutoCapture 中统一启动自动流程
        case .notDetermined:
            AVCaptureDevice.requestAccess(for: .video) { [weak self] granted in
                DispatchQueue.main.async {
                    if granted {
                        self?.setupCamera()
                        self?.setupUI()
                        // 自动流程由 viewDidAppear 中统一处理
                    } else {
                        self?.showCameraPermissionAlert()
                    }
                }
            }
        default:
            showCameraPermissionAlert()
        }
    }
    
    // MARK: - UI设置
    private func setupUI() {
        view.addSubview(overlayView)
        overlayView.frame = view.bounds
    }
    
    // MARK: - 相机设置
    private func setupCamera() {
        captureSession = AVCaptureSession()
        captureSession?.sessionPreset = .high
        
        guard let captureDevice = AVCaptureDevice.default(for: .video) else {
            print("无法获取相机设备")
            return
        }
        
        do {
            let input = try AVCaptureDeviceInput(device: captureDevice)
            
            if let captureSession = captureSession,
               captureSession.canAddInput(input) {
                
                captureSession.addInput(input)
                setupVideoDataOutput(captureSession)
                setupPreviewLayer(captureSession)
                configureCameraSettings(captureDevice)
                
                // 启动相机会话
                DispatchQueue.global(qos: .userInitiated).async {
                    self.captureSession?.startRunning()
                }
                
                print("相机配置成功")
            }
        } catch {
            print("相机配置错误: \(error.localizedDescription)")
        }
    }
    
    // 设置视频数据输出
    private func setupVideoDataOutput(_ session: AVCaptureSession) {
        let dataOutput = AVCaptureVideoDataOutput()
        dataOutput.videoSettings = [
            kCVPixelBufferPixelFormatTypeKey as String: Int(kCVPixelFormatType_32BGRA)
        ]
        dataOutput.setSampleBufferDelegate(self, queue: DispatchQueue.global(qos: .userInteractive))
        
        if session.canAddOutput(dataOutput) {
            session.addOutput(dataOutput)
            videoDataOutput = dataOutput
            
            // 设置视频方向
            if let connection = dataOutput.connection(with: .video) {
                connection.videoOrientation = .portrait
                connection.isVideoMirrored = true
            }
        }
    }
    
    // 设置预览图层
    private func setupPreviewLayer(_ session: AVCaptureSession) {
        previewLayer = AVCaptureVideoPreviewLayer(session: session)
        previewLayer?.videoGravity = .resizeAspect  // 改用 resizeAspect 以保持原始比例
        previewLayer?.frame = view.bounds
        previewLayer?.connection?.videoOrientation = .portrait
        if let previewLayer = previewLayer {
            view.layer.insertSublayer(previewLayer, at: 0)
        }
    }
    
    // 配置相机参数
    private func configureCameraSettings(_ device: AVCaptureDevice) {
        do {
            try device.lockForConfiguration()
            
            // 设置自动曝光
            if device.isExposureModeSupported(.continuousAutoExposure) {
                device.exposureMode = .continuousAutoExposure
            }
            
            // 设置最大曝光时间为 1/100 秒
//            let maxExposureDuration = CMTime(value: 1, timescale: 100)
//            device.activeMaxExposureDuration = maxExposureDuration
            
            // 设置自动白平衡
            if device.isWhiteBalanceModeSupported(.continuousAutoWhiteBalance) {
                device.whiteBalanceMode = .continuousAutoWhiteBalance
            }
            
            // 设置自动对焦
            if device.isFocusModeSupported(.continuousAutoFocus) {
                device.focusMode = .continuousAutoFocus
            }
            
            // 设置相机缩放倍率为1.5x
            let zoomFactor: CGFloat = 1.5
            if zoomFactor <= device.activeFormat.videoMaxZoomFactor {
                device.videoZoomFactor = zoomFactor
            }
            
            device.unlockForConfiguration()
            
            print("相机参数设置：")
            print("- 最大曝光时间: \(device.activeMaxExposureDuration.seconds) 秒")
            print("- ISO范围: \(device.activeFormat.minISO) - \(device.activeFormat.maxISO)")
            print("- 当前ISO: \(device.iso)")
            print("- 当前曝光时间: \(device.exposureDuration.seconds) 秒")
            print("- 当前缩放倍率: \(device.videoZoomFactor)x")
            
        } catch {
            print("相机参数配置错误: \(error.localizedDescription)")
        }
    }
    
    // 停止相机会话
    private func stopCameraSession() {
        captureSession?.stopRunning()
        videoDataOutput = nil
        currentVideoPixelBuffer = nil
    }
    
    // MARK: - 扫描框参数
    func getScanViewWidth() -> CGFloat {
        return overlayView.scanViewWidth
    }
    
    func getCropOffsetWidth() -> CGFloat {
        return overlayView.cropOffsetWidth
    }
    
    func getScanViewLeftMargin() -> CGFloat {
        return overlayView.scanViewLeftMargin
    }
    
    // MARK: - 自动拍摄相关
    private func startAutoCapture(afterDelay delay: TimeInterval = 4.0) {
        guard isAutoCatchImage else {
            print("自动拍照已停止，不启动倒计时")
            return
        }
        
        if delay > 0 {
            print("【自动拍照】将在 \(delay) 秒后开始捕获")
        } else {
            print("【自动拍照】立即开始捕获")
        }

        print("【自动拍照】startAutoCapture 调用")
        DispatchQueue.main.asyncAfter(deadline: .now() + delay) { [weak self] in
            guard let self = self,
                  self.isAutoCatchImage else {
                print("自动拍照已取消，停止执行")
                return
            }
            
            self.retryCount = 0
            print("【自动拍照】开始第 1 次截图")
            self.captureScreen()
        }
    }
    
    func confirmErrorPopupShow(errorMessage: String) {
        let popupView = ConfirmErrorPopupViewController(errorMessage: errorMessage, delegate: self)
        popupView.modalTransitionStyle = .crossDissolve
        present(popupView, animated: true)
    }
    
    func didTapConfirm() {
        // 让用户自行决定下一步：停止自动拍照，保留界面
        print("【自动拍照】用户确认提示后，停止自动拍照，等待用户手动操作")

        stopAutoCapture()
        retryCount = 0 // 如需之后重新启动，保持计数归零

        // 恢复扫描动画，提示用户可以重新调整并点拍照按钮
        overlayView.scanView.startAnimation()
    }
    
    func didTapPopupCancel() {
        // 用户点击弹窗取消时，如果重试次数未达到最大值，继续自动拍照
        if retryCount < kMaxRetryAttempts {
            isAutoCatchImage = true
            startAutoCapture(afterDelay: kRetryDelay)
        } else {
            // 如果已达到最大重试次数，停止自动拍照
            isAutoCatchImage = false
            print("已达到最大重试次数")
        }
    }
    
    // 重试截图
    func retryCapture(with errorMessage: String = "") {
        // 检查是否允许自动拍照
        guard isAutoCatchImage else {
            print("自动拍照已停止")
            return
        }
        
        retryCount += 1
        print("【自动拍照】准备重试，第 \(retryCount) 次")
        
        if retryCount >= kMaxRetryAttempts {
            print("Max capture attempts reached")
            isAutoCatchImage = false
            if !errorMessage.isEmpty {
                confirmErrorPopupShow(errorMessage: errorMessage)
            }
            return
        }
        
        print("Retrying capture... Attempt: \(retryCount + 1) 将在 \(kRetryDelay)s 后执行")
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.overlayView.startCountdown(seconds: Int(self.kRetryDelay)) { [weak self] in
                guard let self = self,
                      self.isAutoCatchImage else {
                    print("自动拍照已取消")
                    return
                }
                print("【自动拍照】重试截图，第 \(self.retryCount + 1) 次")
                self.captureScreen()
            }
        }
    }
    
    // 自动截图入口
    func autoCatchImage() {
        guard isAutoCatchImage else {
            print("自动拍照已停止")
            return
        }
        captureScreen()
    }
    
    // 重置状态
    func resetCaptureState() {
        retryCount = 0
        
        // 只要用户是点取消回来的，一律新启动自动拍照
        didTapPopupCancel()
    }
    
    private func stopAutoCapture() {
        print("停止自动拍照 - 之前状态: isAutoCatchImage=\(isAutoCatchImage)")
        isAutoCatchImage = false
        overlayView.stopCountdown()
        print("【自动拍照】stopAutoCapture 被调用")
        print("停止自动拍照 - 之后状态: isAutoCatchImage=\(isAutoCatchImage)")
    }
    
    // MARK: - 图像处理
    private func captureScreen() {
        // 确保开始新截图前清空旧倒计时，避免并行 Timer
        overlayView.stopCountdown()
        print("【自动拍照】captureScreen 入口，isAutoCatchImage=\(isAutoCatchImage)")
        print("准备开始截图 - 当前状态: isAutoCatchImage=\(isAutoCatchImage)")
        
        // 检查是否允许自动拍照
        guard isAutoCatchImage else {
            print("自动拍照已停止，取消截图")
            return
        }
        
        guard let pixelBuffer = currentVideoPixelBuffer else {
            print("没有可用的视频帧")
            return
        }
        
        // 创建图像
        let ciImage = CIImage(cvPixelBuffer: pixelBuffer)
        let context = CIContext()
        guard let cgImage = context.createCGImage(ciImage, from: ciImage.extent) else {
            print("创建图像失败")
            return
        }
        
        let screenshot = UIImage(cgImage: cgImage)
        print("【自动拍照】已获取原始截图，尺寸：\(screenshot.size)")
        print("截图尺寸: \(screenshot.size)")
        
        // 再次检查状态
        guard isAutoCatchImage else {
            print("图像处理过程中自动拍照被取消")
            return
        }
        
        // 处理镜像
        let flippedScreenshot = createFlippedImage(screenshot)
        print("【自动拍照】已完成镜像处理，开始裁剪")
        
        // 最后一次检查状态
        guard isAutoCatchImage else {
            print("镜像处理后自动拍照被取消")
            return
        }
        
        // 裁剪图像
        cropAndProcessImage(flippedScreenshot)
    }
    
    // 创建镜像图像
    private func createFlippedImage(_ image: UIImage) -> UIImage {
        UIGraphicsBeginImageContextWithOptions(image.size, false, image.scale)
        let context = UIGraphicsGetCurrentContext()!
        context.scaleBy(x: -1.0, y: 1.0)
        context.translateBy(x: -image.size.width, y: 0)
        image.draw(in: CGRect(origin: .zero, size: image.size))
        let flippedImage = UIGraphicsGetImageFromCurrentImageContext()!
        UIGraphicsEndImageContext()
        return flippedImage
    }
    
    // 裁剪和处理图像
    @discardableResult
    func cropAndProcessImage(_ image: UIImage, autoProcess: Bool = true) -> UIImage? {
        // 获取预览图层的实际显示区域
        guard let previewLayer = previewLayer else { return nil }
        
        // 计算预览图层中实际显示的图像区域
        let previewLayerBounds = previewLayer.bounds
        
        // 计算实际显示的图像尺寸和位置
        let scale = min(previewLayerBounds.width / image.size.width, previewLayerBounds.height / image.size.height)
        let scaledImageSize = CGSize(width: image.size.width * scale, height: image.size.height * scale)
        let imageX = (previewLayerBounds.width - scaledImageSize.width) / 2
        let imageY = (previewLayerBounds.height - scaledImageSize.height) / 2
        
        // 获取扫描框在预览图层中的位置
        let scanViewFrame = overlayView.scanView.convert(overlayView.scanView.bounds, to: view)
        
        // 计算扫描框在预览图像中的相对位置
        let relativeX = (scanViewFrame.origin.x - imageX) / scaledImageSize.width
        let relativeY = (scanViewFrame.origin.y - imageY) / scaledImageSize.height
        let relativeWidth = scanViewFrame.width / scaledImageSize.width
        let relativeHeight = scanViewFrame.height / scaledImageSize.height
        
        // 将相对位置转换为原始图片上的坐标
        let cropX = relativeX * image.size.width
        let cropY = relativeY * image.size.height
        let cropWidth = relativeWidth * image.size.width
        let cropHeight = relativeHeight * image.size.height
        
        // 确保裁剪区域不超出图像范围
        let cropRect = CGRect(
            x: max(0, cropX),
            y: max(0, cropY),
            width: min(cropWidth, image.size.width - cropX),
            height: min(cropHeight, image.size.height - cropY)
        )
        
        print("裁剪参数：")
        print("- 原始尺寸: \(image.size)")
        print("- 预览图层尺寸: \(previewLayerBounds.size)")
        print("- 缩放后图像尺寸: \(scaledImageSize)")
        print("- 图像位置: x=\(imageX), y=\(imageY)")
        print("- 扫描框位置: \(scanViewFrame)")
        print("- 相对位置: x=\(relativeX), y=\(relativeY), w=\(relativeWidth), h=\(relativeHeight)")
        print("- 最终裁剪区域: \(cropRect)")
        
        if let croppedCGImage = image.cgImage?.cropping(to: cropRect) {
            let croppedImage = UIImage(cgImage: croppedCGImage, scale: image.scale, orientation: image.imageOrientation)
            if autoProcess {
                handleProcessedImage(croppedImage, fullImage: image)
            }
            return croppedImage
        }
        return nil
    }
    
    // 处理最终图像
    private func handleProcessedImage(_ croppedImage: UIImage, fullImage: UIImage) {
        print("【自动拍照】截图裁剪完成，尺寸：\(croppedImage.size)，发送至上层处理…")
            print("正常模式：发送图片到代理")
            self.actionDelegate?.didCaptureImage(croppedImage)
    }
    
    // MARK: - 错误处理
    private func showCameraPermissionAlert() {
        let alert = UIAlertController(
            title: "Camera Access Required",
            message: "Please allow camera access in Settings to use this feature",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "Settings", style: .default) { _ in
            if let url = URL(string: UIApplication.openSettingsURLString) {
                UIApplication.shared.open(url)
            }
        })
        
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        
        present(alert, animated: true)
    }
    
}

// MARK: - AVCaptureVideoDataOutputSampleBufferDelegate
extension FullScreenCameraViewController: AVCaptureVideoDataOutputSampleBufferDelegate {
    func captureOutput(_ output: AVCaptureOutput, didOutput sampleBuffer: CMSampleBuffer, from connection: AVCaptureConnection) {
        guard let pixelBuffer = CMSampleBufferGetImageBuffer(sampleBuffer) else {
            return
        }
        currentVideoPixelBuffer = pixelBuffer
    }
}

// MARK: - OverlayViewDelegate
extension FullScreenCameraViewController: OverlayViewDelegate {
    func didTapCancel() { // navigation bar left back button
        overlayView.scanView.stopAnimation()
        stopAutoCapture()
        didTapBackButton()
    }
    
    func didTapTakePhoto() {
        stopAutoCapture()
        
        guard let pixelBuffer = currentVideoPixelBuffer else {
            print("No available video frame")
            return
        }
        
        // 创建图像
        let ciImage = CIImage(cvPixelBuffer: pixelBuffer)
        let context = CIContext()
        guard let cgImage = context.createCGImage(ciImage, from: ciImage.extent) else {
            print("Failed to create image")
            return
        }
        
        let screenshot = UIImage(cgImage: cgImage)
        let flippedScreenshot = createFlippedImage(screenshot)
        
        // 裁剪并处理图片
        if let croppedImage = cropAndProcessImage(flippedScreenshot, autoProcess: false) {
            // 发送裁剪后的图片到服务器
            actionDelegate?.didCaptureImage(croppedImage)
        }
    }
    
    func didTapChooseFromGallery() {
        overlayView.scanView.stopAnimation()
        stopAutoCapture()
        actionDelegate?.chooseFromGallery()
    }
}
