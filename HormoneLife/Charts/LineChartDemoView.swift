//
//  LineChartDemoView.swift
//  HormoneLife
//
//  Created by Tank on 2024/8/27.
//

import UIKit
import DGCharts

class LineChartConfig {
    
    // control line chart detail view expand more than one screen and can scroll
    static let isLineChartScrollEnbled: Bool = true
    
    // if not scroll
    // tableViewCell.left.right.inset = 10*2
    // yAxisValueView.left.inset = 8, yAxisValueView.width = 16
    // scrollView.left.equalTo(yAxisValueView.snp.right).offset(2)
    // scrollView.right.inset = 10
    // so total width of lineChart = screenWidth - (10*2 + 8 + 16 + 2 + 10)
    static let lineChartWidth = Int(UIScreen.main.bounds.width - 56)
    
    static let lineChartHeight = 320 //从y = 0 开始往上的整个表格高
    static let defaultXAxisHeight = 18 //xAxis隐藏后，y = 0下面还会有一个默认高度。maskAxis0WhiteView
    static let lineChartBottonXViewHeight = 74 //下面 CD + Month view
    static let everyDayXWidth = 20
    static let lineChartDemoViewDefaultHeight = LineChartConfig.lineChartHeight + LineChartConfig.lineChartBottonXViewHeight + 40  //整个view高
}

protocol LineChartDemoViewDelegate: AnyObject {
    func didTapPreChart(chartType: LineChartDemoView.ChartType)
    func didTapNextChart(chartType: LineChartDemoView.ChartType)
    func didTapSelf(chartType: LineChartDemoView.ChartType)
}

extension LineChartDemoViewDelegate {
    func didTapPreChart(chartType: LineChartDemoView.ChartType) {}
    func didTapNextChart(chartType: LineChartDemoView.ChartType) {}
    func didTapSelf(chartType: LineChartDemoView.ChartType) {}
}

protocol LineChartDemoViewDataProtocol: AnyObject {
    func didSelectValue(value: String, chartType: LineChartDemoView.ChartType, cycleDay: String, date: String, index: Int)
}

extension LineChartDemoViewDataProtocol {
    func didSelectValue(value: String, chartType: LineChartDemoView.ChartType, cycleDay: String, date: String, index: Int) {}
}

/// already set default height,
/// outside just need to create like: let lineView = LineChartDemoView(),
/// and add to the superView,
/// and set the position layout: make.left.right.top.equalToSuperview().inset(20),
/// no need to set height/bottom
class LineChartDemoView: UIView {

    let backView: UIView = {
        let v = UIView()
        v.backgroundColor = .white
        return v
    }()
    
    let maskAxis0WhiteView: UIView = {
        let v = UIView()
        v.backgroundColor = .white
        return v
    }()
    
    lazy var yAxisValueView: UIView = {
        let v = UIView()
        v.backgroundColor = .white
        v.isHidden = self.chartType.isYAxisValueStackViewHidden
        return v
    }()
    
    let monthAndCDStackView: UIStackView = {
        let stackview = UIStackView()
        stackview.distribution = .fillEqually
        stackview.axis = .vertical
        stackview.spacing = 12
        stackview.backgroundColor = .clear
        return stackview
    }()
    
    lazy var positiveLabel: UILabel = {
        let l = UILabel()
        l.font = .boldGilroyFont(8)
        l.textColor = .mainTextColor.withAlphaComponent(0.6)
        l.textAlignment = .left
        l.text = "POSITIVE"
        l.isHidden = chartType.isPositiveAndNegativeHidden
        return l
    }()
    
    lazy var negativeLabel: UILabel = {
        let l = UILabel()
        l.font = .boldGilroyFont(8)
        l.textColor = .mainTextColor.withAlphaComponent(0.6)
        l.textAlignment = .left
        l.text = "NEGATIVE"
        l.isHidden = chartType.isPositiveAndNegativeHidden
        return l
    }()
    
    lazy var scrollView: UIScrollView = {
        let everyDayXwidth = horizontalCDListData.count * LineChartConfig.everyDayXWidth < LineChartConfig.lineChartWidth ? LineChartConfig.lineChartWidth / horizontalCDListData.count : LineChartConfig.everyDayXWidth
        
        let lineChartViewWidth = isLineChartScrollEnbled ? horizontalCDListData.count * everyDayXwidth : LineChartConfig.lineChartWidth
        let s = UIScrollView()
        s.backgroundColor = .clear
        s.contentSize = CGSize(width: lineChartViewWidth, height: LineChartConfig.lineChartHeight + LineChartConfig.lineChartBottonXViewHeight)
        s.isScrollEnabled = true
        s.showsHorizontalScrollIndicator = true
        s.layer.cornerRadius = 4
        s.setContentOffset(CGPoint(x: 0, y: 0), animated: false)
        return s
    }()
    
    lazy var preChartBtn: UIButton = {
        let b = UIButton(type: .custom)
        b.isHidden = true
        b.setImage(UIImage(named: "chartPreBtn"), for: .normal)
        b.addTarget(self, action: #selector(didTapPre), for: .touchUpInside)
        return b
    }()
    
    lazy var nextChartBtn: UIButton = {
        let b = UIButton(type: .custom)
        b.isHidden = true
        b.setImage(UIImage(named: "chartNextBtn"), for: .normal)
        b.addTarget(self, action: #selector(didTapNext), for: .touchUpInside)
        return b
    }()
    
    let horizontalEachDayStackView: UIStackView = {
        let stackview = UIStackView()
        stackview.distribution = .fillEqually
        stackview.axis = .horizontal
        stackview.spacing = 0
        stackview.backgroundColor = .themeColor
        return stackview
    }()
    
    let yFormatter: NumberFormatter = {
        let f = NumberFormatter()
        f.minimumFractionDigits = 0
        f.maximumFractionDigits = 2
        return f
    }()
    
    var cdAngleMarkViews: [UIImageView] = []
    var dateAngleMarkViews: [UIImageView] = []
    
    lazy var lineChartView: LineChartView = {
        let chart = LineChartView()
        chart.backgroundColor = .clear
        chart.doubleTapToZoomEnabled = false
        chart.setScaleEnabled(false)
        chart.pinchZoomEnabled = false
        chart.dragEnabled = !isLineChartScrollEnbled  //关闭 触点在lineChart上移动时切换锚点，这样就可以将移动手势传到底部的scrollView上。
        //chart.isUserInteractionEnabled = false
        
        // x轴
        chart.xAxis.enabled = false  //是否显示x轴的label，默认显示在上方
        chart.xAxis.labelPosition = .bottom  // x轴数值位置
        chart.xAxis.yOffset = 10 //x轴数值位置偏移量，越大离x轴越远
        chart.xAxis.axisMaximum = Double(self.dayEnd)
        chart.xAxis.axisMinimum = Double(self.dayStart)
        chart.xAxis.labelCount = horizontalCDListData.count // = setLabelCount(31, force: true)
        chart.xAxis.drawGridLinesEnabled = false // 网格中每一条x线（竖线）
        //chart.xAxis.centerAxisLabelsEnabled = true //x label 居中
        chart.xAxis.labelFont = .regularGilroyFont(10)
        chart.xAxis.labelTextColor = .mainTextColor
        
        
        // y轴
        let leftAxis = chart.leftAxis
        //leftAxis.enabled = false
        leftAxis.axisMaximum = Double(self.maxYValue)
        leftAxis.axisMinimum = Double(self.minYValue)
        leftAxis.labelCount = self.howManySection// + 1  //12: 比如平均分成5，则LabelCount = （leftAxis.axisMaximum / 5） + 1 （+1是因为有一个0%）
        leftAxis.valueFormatter = LeftAxisValueFormatter()  // %比, 默认没有%
        leftAxis.xOffset = 0  //与y轴偏移量
        leftAxis.labelFont = .regularGilroyFont(0.1)
        leftAxis.labelTextColor = .clear
        leftAxis.axisLineColor = .clear //去掉X=0这和条
        leftAxis.gridColor = .themeColor  //横线颜色
        
        // 右边y轴
        chart.rightAxis.enabled = false
        
        self.balloonMarker.chartView = chart
        chart.marker = balloonMarker
        
        chart.delegate = self
        return chart
    }()
    
    lazy var balloonMarker: XYMarkerView = {
        let marker = XYMarkerView(color: .themeColor.withAlphaComponent(0.9),
                                  font: .regularGilroyFont(12),
                                   textColor: .mainTextColor,
                                   insets: UIEdgeInsets(top: 8, left: 8, bottom: 20, right: 8),
                                   xAxisValueFormatter: LeftAxisValueFormatter())
        marker.yFormatter = yFormatter
        marker.minimumSize = CGSize(width: 150, height: 80)
        marker.monthDayList = monthAndDayList
        marker.chartType = self.chartType
        marker.testResultList = self.cycleData?.testPageResultList ?? []
        marker.temperatureUnit = self.temperatureUnit
        marker.resultList = self.resultList
        return marker
    }()
    
    var cdList: [Int] = []
    lazy var horizontalCDListData: [String] = {
        //let minute = self.dayStart...self.dayEnd
        //let array = minute.map { String(format: "%02d", $0) }
        let minute = cdList
        let array = minute.map { String(format: "%d", $0) }
        return array
    }()
    
    var dateList: [String] = []
    lazy var horizontalDateListData: [String] = {
        let d = dateList.map { $0.components(separatedBy: "-").last ?? "" }
        return d.map { String(Int($0) ?? 0) }
    }()
    lazy var monthAndDayList: [String] = {
        dateList.map { d in
            let mon = d.yyyyMMddHHmmss_enMM().prefix(3)
            let day = d.yyyyMMddHHmmss_dd()
            return mon + " " + day
        }
    }()
    
    var resultList: [Double] = []
    
    var calendarDetailVOList: [CalendarDetailVO] = []
    lazy var calendarLabelList: [[CalendarLabel]] = {
        calendarDetailVOList.map { CalendarLabel.dateState(detail: $0) }
    }()
    
    lazy var yAxisValues: [String] = {
        var values: [Double] = []
        let eachValue = Double(self.maxYValue - self.minYValue) / Double(self.howManySection)
        for i in 0...self.howManySection {
            values.append(Double(i) * eachValue + Double(self.minYValue))
        }
        return self.chartType.formatterYAxisValues(values: values)
    }()
    
    var isLineChartScrollEnbled = false
    weak var delegate: LineChartDemoViewDelegate?
    weak var dataProtocol: LineChartDemoViewDataProtocol?
    var lineChartData: [String] = []
    var dayStart: Int = 0
    var dayEnd: Int = 0
    var minYValue: Double = 0
    var maxYValue: Double = 1
    var howManySection: Int = 1
    var date: Date = Date()
    var temperatureUnit: Int = 1
    
    var chartType: ChartType = .temp {
        didSet {
            switch chartType {
            case .lhUltra:
                self.minYValue = 0
                self.maxYValue = 65
                self.howManySection = 13
            case .fsh:
                self.minYValue = 0
                self.maxYValue = 55
                self.howManySection = 11
            case .lh:
                self.minYValue = 0
                self.maxYValue = 2.5
                self.howManySection = 25
            case .pdg:
                    self.minYValue = -10
                    self.maxYValue = 10
                    self.howManySection = 2
            case .hcg:
                    self.minYValue = -10
                    self.maxYValue = 10
                    self.howManySection = 2
            case .temp:
                self.minYValue = temperatureUnit == 1 ? 32 : 92
                self.maxYValue = temperatureUnit == 1 ? 42 : 108
                self.howManySection = temperatureUnit == 1 ? 10 : 8
            case .conception:
                self.minYValue = 0
                self.maxYValue = 55
                self.howManySection = 11
            }
        }
    }
    
    var cycleData: CycleFoldLineStatistic? = nil {
        didSet {
                positiveLabel.isHidden = chartType.isPositiveAndNegativeHidden
                negativeLabel.isHidden = chartType.isPositiveAndNegativeHidden
                yAxisValueView.isHidden = chartType.isYAxisValueStackViewHidden
                
                self.cdList = cycleData?.cdList ?? []
                self.dayStart = cdList.first ?? 0
                self.dayEnd = cdList.last ?? 0
                
                self.dateList = cycleData?.dateList ?? []
                self.date = dateList.first?.covertDate(with: "yyyy-MM-dd") ?? Date()
                
                self.resultList = cycleData?.resultList ?? []
                
                self.calendarDetailVOList = cycleData?.calendarDetailVOList ?? []
                
                setupView()
        }
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
    }
    
    private func setupView() {
        setupUI()
        setupHorizontalData()
        setupYAxisStackView()
        setupMonthAndCDStackView()
        setupLineChart()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        addSubview(backView)
        [yAxisValueView, scrollView, monthAndCDStackView, positiveLabel, negativeLabel].forEach(backView.addSubview)
        [horizontalEachDayStackView, lineChartView, maskAxis0WhiteView, nextChartBtn, preChartBtn].forEach(scrollView.addSubview)
        
        backView.snp.makeConstraints { make in
            make.height.equalTo(LineChartConfig.lineChartDemoViewDefaultHeight)
            make.edges.equalToSuperview()
        }
        
        yAxisValueView.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(20)
            make.left.equalToSuperview().inset(8)
            make.width.equalTo(16)
            make.height.equalTo(LineChartConfig.lineChartHeight + 4)
        }
        
        scrollView.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview().inset(20)
            make.right.equalToSuperview().inset(10)
            make.left.equalTo(yAxisValueView.snp.right).offset(2)
        }
        
        horizontalEachDayStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalTo(scrollView.contentSize.height)
            make.width.equalTo(scrollView.contentSize.width)
        }
        
        lineChartView.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview()
            make.height.equalTo(LineChartConfig.lineChartHeight + LineChartConfig.defaultXAxisHeight)
        }
        
        maskAxis0WhiteView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalToSuperview().inset(LineChartConfig.lineChartHeight)
            make.height.equalTo(10)
        }
        
        monthAndCDStackView.snp.makeConstraints { make in
            make.top.equalTo(maskAxis0WhiteView.snp.bottom)
            make.right.equalTo(yAxisValueView.snp.right)
            make.height.equalTo(48)
            make.width.equalTo(16)
        }
        
        preChartBtn.snp.makeConstraints { make in
            make.height.width.equalTo(40)
            make.centerY.equalTo(lineChartView).offset(-4)
            make.left.equalToSuperview().inset(-10)
        }
        
        nextChartBtn.snp.makeConstraints { make in
            make.height.width.equalTo(40)
            make.centerY.equalTo(lineChartView).offset(-4)
            make.right.equalToSuperview().inset(-10)
        }
        
        positiveLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().inset(12)
            make.bottom.equalTo(scrollView.snp.top).offset(-2)
        }
        
        negativeLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().inset(12)
            make.bottom.equalTo(maskAxis0WhiteView)
        }
        
        let tapGuesture = UITapGestureRecognizer(target: self, action: #selector(didTapChart))
        addGestureRecognizer(tapGuesture)
    }
    
    @objc func didTapChart() {
        delegate?.didTapSelf(chartType: chartType)
    }
    
    private func setupLineChart() {
        var dataEntries: [ChartDataEntry] = []
        var validDataEntries: [ChartDataEntry] = []

        for (index, _) in horizontalCDListData.enumerated() {
            var yValue: Double = 0
            var shouldAddEntry = true

            if resultList.count > index {
                // 检查是否为 -1（没有用户录入的数据）
                if resultList[index] == -1 {
                    shouldAddEntry = false
                } else if chartType == .pdg {
                    if let criticalV = HomeDataSingleton.shared().homeData?.pdgCriticalValue {

//                        yValue = resultList[index] - Double(criticalV)
//                        if yValue > Double(criticalV) {
//                            yValue = Double(criticalV)
//                        }

                        if resultList[index] == 0 {
                            yValue = 0

                            if let lastResultLabel = self.cycleData?.testPageResultList[index].lastResultLabel {
                                yValue = lastResultLabel == "Positive" ? 8 : -8
                            }
                        } else {
                            yValue = resultList[index] < Double(criticalV) ? 8 : -8
                        }

                    }
                } else if chartType == .hcg {
                    if let criticalV = HomeDataSingleton.shared().homeData?.hcgCriticalValue {

//                        yValue = resultList[index] - Double(criticalV)
//                        if yValue > Double(criticalV) {
//                            yValue = Double(criticalV)
//                        }

                        if resultList[index] == 0 {
                            yValue = 0

                            if let lastResultLabel = self.cycleData?.testPageResultList[index].lastResultLabel {
                                yValue = lastResultLabel == "Positive" ? 8 : -8
                            }
                        } else {
                            yValue = resultList[index] < Double(criticalV) ? -8 : 8
                        }
                    }
                } else if chartType == .temp {
                    if temperatureUnit == 1, resultList[index] < 32 {
                        yValue = 32
                    } else if temperatureUnit == 1, resultList[index] > 42 {
                        yValue = 42
                    } else if temperatureUnit == 2, resultList[index] < 92 {
                        yValue = 92
                    } else if temperatureUnit == 2, resultList[index] > 108 {
                        yValue = 108
                    } else {
                        yValue = resultList[index]
                    }

                }else {
                    yValue = resultList[index]
                }

                // 只有当 shouldAddEntry 为 true 时才添加数据点
                if shouldAddEntry {
                    let entry = ChartDataEntry(x: Double(index + 1), y: yValue)
                    dataEntries.append(entry)
                    validDataEntries.append(entry)
                }
            }
        }

        // 创建连续的数据段来处理断开的线
        let dataSets = createContinuousDataSets(from: validDataEntries)

        //add data into chart
        let data = LineChartData(dataSets: dataSets)
        lineChartView.data = data
        
        if chartType == .conception {
            // conception default show current day or last day of current period data.
            // today
            let today = Date().yyyyMMdd()
            if let index = dateList.firstIndex(of: today),
               let entry = validDataEntries.first(where: { Int($0.x) - 1 == index }) {
                chartValueSelected(lineChartView, entry: entry, highlight: Highlight())
            } else if let lastEntry = validDataEntries.last {
                chartValueSelected(lineChartView, entry: lastEntry, highlight: Highlight())
            }
        } else {
            // Others will default show the max value day data.
            if let maxValue = resultList.max(),
               let maxValueIndex = resultList.firstIndex(of: maxValue),
               let entry = validDataEntries.first(where: { Int($0.x) - 1 == maxValueIndex }) {
                chartValueSelected(lineChartView, entry: entry, highlight: Highlight())
            }
        }
    }

    // 创建连续的数据段来处理断开的线
    private func createContinuousDataSets(from entries: [ChartDataEntry]) -> [LineChartDataSet] {
        var dataSets: [LineChartDataSet] = []
        var currentSegment: [ChartDataEntry] = []

        // 按 x 值排序
        let sortedEntries = entries.sorted { $0.x < $1.x }

        for (index, entry) in sortedEntries.enumerated() {
            currentSegment.append(entry)

            // 检查是否需要开始新的段
            let isLastEntry = index == sortedEntries.count - 1
            let nextEntryExists = !isLastEntry
            let hasGap = nextEntryExists && sortedEntries[index + 1].x > entry.x + 1

            if isLastEntry || hasGap {
                // 创建当前段的数据集
                if !currentSegment.isEmpty {
                    let dataSet = createLineChartDataSet(entries: currentSegment)
                    dataSets.append(dataSet)
                    currentSegment.removeAll()
                }
            }
        }

        return dataSets
    }

    // 创建单个 LineChartDataSet 的配置
    private func createLineChartDataSet(entries: [ChartDataEntry]) -> LineChartDataSet {
        let dataSet = LineChartDataSet(entries: entries, label: "")
        dataSet.drawCirclesEnabled = false //是否显示数据点为一个圆
        dataSet.setColors(UIColor.labelBackColor)
        dataSet.drawValuesEnabled = false //是否显示数据点数值

        // 拆线下方颜色设置
        let gradientColors = [
            UIColor.themeColor.withAlphaComponent(1).cgColor,
            UIColor.themeColor.withAlphaComponent(0).cgColor
        ]
        let gradient = CGGradient(colorsSpace: nil, colors: gradientColors as CFArray, locations: [1, 0])!
        dataSet.fillAlpha = (chartType == .pdg || chartType == .hcg) ? 0 : 1
        dataSet.drawFilledEnabled = true
        dataSet.fill = LinearGradientFill(gradient: gradient, angle: 90)

        return dataSet
    }

    private func setupHorizontalData() {
        
        for (index, item) in horizontalCDListData.enumerated() {
            let hView = UIView()
            //hView.backgroundColor = UIColor(red: CGFloat(arc4random() % 250)/255.0, green: 200/255.0, blue: 100/255.0, alpha: 0.3)
            hView.backgroundColor = .white
            hView.tag = index
            
            let heartMarkView: UIImageView = {
                let v = UIImageView()
                v.contentMode = .scaleAspectFit
                return v
            }()
            
            let label1 = UILabel()
            label1.font = .regularGilroyFont(8)
            label1.textColor = .mainTextColor
            label1.textAlignment = .center
            label1.backgroundColor = .clear
            label1.text = item
            
            let whiteView2 = UIImageView()
            whiteView2.contentMode = .top
            whiteView2.backgroundColor = .white
            whiteView2.image = nil
            
            let angleMarkView2: UIImageView = {
                let v = UIImageView()
                v.contentMode = .scaleAspectFit
                v.image = UIImage(named: "trangleForChart")
                v.isHidden = true
                v.tag = 1000 + index
                return v
            }()
            
            let label2 = UILabel()
            label2.font = .regularGilroyFont(8)
            label2.textColor = .mainTextColor
            label2.backgroundColor = .white
            label2.textAlignment = .center
            if horizontalDateListData.count > index {
                //label2.text = "\(index + 1)"
                label2.text = horizontalDateListData[index]
            }
            
            let whiteView3 = UIImageView()
            whiteView3.contentMode = .top
            whiteView3.backgroundColor = .white
            whiteView3.image = nil
            
            let angleMarkView3: UIImageView = {
                let v = UIImageView()
                v.contentMode = .scaleAspectFit
                v.image = UIImage(named: "trangleForChart")
                v.isHidden = true
                v.tag = 2000 + index
                return v
            }()
            
            [heartMarkView, whiteView2, label2, whiteView3].forEach(hView.addSubview)
            heartMarkView.addSubview(label1)
            whiteView2.addSubview(angleMarkView2)
            whiteView3.addSubview(angleMarkView3)
            
            cdAngleMarkViews.append(angleMarkView2)
            dateAngleMarkViews.append(angleMarkView3)
            
            heartMarkView.snp.makeConstraints { make in
                make.left.right.equalToSuperview()
                make.top.equalToSuperview().inset(LineChartConfig.lineChartHeight + 10)
                make.height.equalTo(18)
            }
            
            label1.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
            
            whiteView2.snp.makeConstraints { make in
                make.left.right.equalToSuperview()
                make.top.equalTo(label1.snp.bottom)
                make.height.equalTo(12)
            }
            
            angleMarkView2.snp.makeConstraints { make in
                make.edges.equalToSuperview().inset(1)
            }
            
            label2.snp.makeConstraints { make in
                make.left.right.equalToSuperview()
                make.top.equalTo(whiteView2.snp.bottom)
                make.height.equalTo(18)
            }
            
            whiteView3.snp.makeConstraints { make in
                make.left.right.bottom.equalToSuperview()
                make.top.equalTo(label2.snp.bottom)
            }
            
            angleMarkView3.snp.makeConstraints { make in
                make.width.height.equalTo(angleMarkView2)
                make.top.left.right.equalToSuperview().inset(1)
            }
            
            if calendarLabelList.count > index {
                let dateTypies = calendarLabelList[index]
                dateTypies.forEach { type in
                    hView.backgroundColor = type.chartTypeColor
                    if type == .sex {
                        heartMarkView.image = UIImage(named: type.chartTypeImageName)
                        //angleMarkView3.image = UIImage(named: type.chartTypeImageName)
                    }
                }
            }
            
            horizontalEachDayStackView.addArrangedSubview(hView)
        }
    }
    
    private func setupYAxisStackView() {
        let eachlabelHeight = (LineChartConfig.lineChartHeight - 10) / (yAxisValues.count - 1)
        for i in 0..<yAxisValues.count {
            let label = UILabel()
            label.textColor = .mainTextColor
            label.font = .regularGilroyFont(10)
            label.text = yAxisValues[i]
            label.textAlignment = .right
            label.tag = i
            //label.backgroundColor = UIColor(red: CGFloat(arc4random() % 250)/255.0, green: 200/255.0, blue: 100/255.0, alpha: 0.3)
            let offset: Double = 6 + Double(i) * 0.3
            yAxisValueView.addSubview(label)
            label.snp.makeConstraints { make in
                make.right.equalToSuperview()
                make.top.equalToSuperview().inset(offset + Double(i * eachlabelHeight))
            }
        }
    }
    
    private func setupMonthAndCDStackView() {
        let data = ["CD", "\(date.enMonth.prefix(3))"]
        
        for (index, item) in data.enumerated() {
            let label = UILabel()
            label.textColor = .mainTextColor
            label.font = .mediumGilroyFont(8)
            label.text = item
            label.textAlignment = .right
            label.frame = CGRect(x: 0, y: 0, width: 16, height: 18)
            label.tag = index
            monthAndCDStackView.addArrangedSubview(label)
        }
    }
    
    @objc func didTapNext() {
        print(#function)
        delegate?.didTapNextChart(chartType: self.chartType)
    }
    
    @objc func didTapPre() {
        print(#function)
        delegate?.didTapPreChart(chartType: self.chartType)
    }
}

extension LineChartDemoView: ChartViewDelegate {
    @objc func chartValueSelected(_ chartView: ChartViewBase, entry: ChartDataEntry, highlight: Highlight) {
        
        guard let value = yFormatter.string(from: NSNumber(floatLiteral: entry.y)) else { return }
//        if value == "0" || value == "-0" || chartType == .pdg || chartType == .hcg {
        if chartType == .pdg || chartType == .hcg {
            balloonMarker.color = .themeColor.withAlphaComponent(0)
            balloonMarker.textColor = .mainTextColor.withAlphaComponent(0)
        } else {
            balloonMarker.color = .themeColor.withAlphaComponent(0.9)
            balloonMarker.textColor = .mainTextColor.withAlphaComponent(1)
        }
        
        if let testResultList = cycleData?.testPageResultList, testResultList.count > Int(entry.x) - 1 {
            let result = testResultList[Int(entry.x) - 1]
            if value == "0" && result.lastResult == nil {
                balloonMarker.color = .themeColor.withAlphaComponent(0)
                balloonMarker.textColor = .mainTextColor.withAlphaComponent(0)
            }
        }
        
        if chartType == .temp, resultList.count > Int(entry.x) - 1 {
            if temperatureUnit == 1 && resultList[Int(entry.x) - 1] == 0 {
                balloonMarker.color = .themeColor.withAlphaComponent(0)
                balloonMarker.textColor = .mainTextColor.withAlphaComponent(0)
            }
            
            if temperatureUnit == 2 && resultList[Int(entry.x) - 1] == 0 {
                balloonMarker.color = .themeColor.withAlphaComponent(0)
                balloonMarker.textColor = .mainTextColor.withAlphaComponent(0)
            }
        }
        
        var dateStr = ""
        if monthAndDayList.count > Int(entry.x) - 1 {
            dateStr = monthAndDayList[Int(entry.x) - 1]
        }
        
        for (index, item) in cdAngleMarkViews.enumerated() {
            item.isHidden = index != Int(entry.x) - 1
        }
        for (index, item) in dateAngleMarkViews.enumerated() {
            item.isHidden = index != Int(entry.x) - 1
        }
        
        dataProtocol?.didSelectValue(value: value, chartType: self.chartType, cycleDay: LeftAxisValueFormatter().stringForValue(entry.x, axis: XAxis()), date: dateStr, index: Int(entry.x - 1))
    }
    
    @objc func chartValueNothingSelected(_ chartView: ChartViewBase) {
        //NSLog("chartValueNothingSelected");
    }
}

extension LineChartDemoView {
    
    static func chartTypeByTypeString(typeString: String?) -> ChartType? {
        guard let str = typeString else {
            return nil
        }
        switch str.uppercased() {
        case "LH ULTRA":
            return .lhUltra
        case "PDG":
            return .pdg
        case "FSH":
            return .fsh
        case "LH":
            return .lh
        case "HCG":
            return .hcg
        case "CONCEPTION":
            return .conception
        default:
            return .temp
        }
    }
    
    enum ChartType: Int, CaseIterable {
        case lhUltra
        case lh
        case hcg
        case pdg
        case fsh
        case temp
        case conception
        
        var isPositiveAndNegativeHidden: Bool {
            switch self {
            case .lhUltra, .fsh, .lh, .temp, .conception:
                return true
            case .pdg, .hcg:
                return false
            }
        }
        
        var isYAxisValueStackViewHidden: Bool {
            switch self {
            case .lhUltra, .fsh, .lh, .temp, .conception:
                return false
            case .pdg, .hcg:
                return true
            }
        }
        
        func formatterYAxisValues(values: [Double]) -> [String] {
            switch self {
            case .lh:
                var valuesStr = values.map { String(format: "%.1f", $0) }
                //valuesStr.append(">1")
                return valuesStr.reversed()
            case .conception:
                return values.map { "\(Int($0))%" }.reversed()
            default:
                return values.map { "\(Int($0))" }.reversed()
            }
        }
        
        var title: String {
            switch self {
            case .lhUltra: return "LH Ultra"
            case .pdg: return "PdG"
            case .fsh: return "FSH"
            case .lh: return "LH"
            case .hcg: return "HCG"
            default: return ""
            }
        }
        
        var description: String {
            switch self {
            case .lhUltra:
               return "LH Ultra"
            case .pdg:
                return "PdG"
            case .fsh:
                return "FSH"
            case .lh:
                return "LH"
            case .hcg:
                return "HCG"
            default: return ""
            }
        }
        
        var chartViewTitle: String {
            switch self {
            case .lhUltra: return "LH Ultra Chart"
            case .pdg: return "PdG Chart"
            case .fsh: return "FSH Chart"
            case .lh: return "LH Chart"
            case .hcg: return "HCG Chart"
            case .conception: return "Chance of Conception Chart"
            default: return "Body Temperature Chart"
            }
        }
        
        func detailChartViewBottomTitle(with value: String, resultLabel: String? = nil, resultValue: Double? = nil) -> String {
            switch self {
            case .lhUltra:
                let v = Float(value) ?? 0
                var valueLevel = ""
                if v < 25 {
                    valueLevel = "low"
                } else if v >= 25 && v < 45 {
                    valueLevel = "hight"
                } else {
                    valueLevel = "peak"
                }
               return "Today, your LH Ultra level is \(value)"
            case .pdg:
                let v = Float(value) ?? 0
                var valueLevel = ""
                if v > 0 {
                    valueLevel = "Positive"
                } else {
                    valueLevel = "Negative"
                }
                return "Today, Your PdG Result is \(resultLabel ?? valueLevel)"
            case .fsh:
                return "Today, your FSH level is \(value)"
            case .lh:
                let v = Float(value) ?? 0
                var valueLevel = ""
                if v < 0.75 {
                    valueLevel = "low"
                } else if v >= 0.75 && v < 1.0 {
                    valueLevel = "hight"
                } else {
                    valueLevel = "peak"
                }
                return "Today, your LH level is \(value)"
            case .hcg:
                let v = Float(value) ?? 0
                var valueLevel = ""
                if v > 0 {
                    valueLevel = "Positive"
                } else {
                    valueLevel = "Negative"
                }
                return "Today, Your HCG Result Is \(resultLabel ?? valueLevel)"
            case .conception:
                return "Today your chance of conception rate is \(value)%"
            default: return ""
            }
        }
        
        func detailChartViewBottomDescWithValue(value: String, tips: String? = nil) -> String {
//            guard value == "0" || value == "-0" else {
                switch self {
                case .lhUltra:
                    let v = Float(value) ?? 0
                    var valueLevel = ""
                    if v < 25 {
                        valueLevel = "lowed"
                    } else if v >= 25 && v < 45 {
                        valueLevel = "highted"
                    } else {
                        valueLevel = "peaked"
                    }
                    return tips ?? "N/A"//"Based on the results, your LH Ultra level has \(valueLevel)."
                case .pdg:
                    let v = Float(value) ?? 0
                    var valueLevel = ""
                    var isLowOrHight = ""
                    var isOvulated = ""
                    if v > 0 {
                        valueLevel = "Positive"
                        isLowOrHight = "hight"
                        isOvulated = "ovulated"
                    } else {
                        valueLevel = "Negative"
                        isLowOrHight = "low"
                        isOvulated = "not yet ovulated"
                    }
                    return tips ?? "N/A"//"Your result is \(valueLevel)--this means that your PdG level is \(isLowOrHight) and you may have \(isOvulated)."
                case .fsh:
                    return tips ?? "N/A"//"If you get two or more FSH test results below 10, with some even below 5 after testing for three day."
                case .lh:
                    let v = Float(value) ?? 0
                    var valueLevel = ""
                    if v < 0.75 {
                        valueLevel = "lowed"
                    } else if v >= 0.75 && v < 1.0 {
                        valueLevel = "highted"
                    } else {
                        valueLevel = "peaked"
                    }
                    return tips ?? "N/A"//"Based on the results, your LH Ultra level has \(valueLevel)."
                case .hcg:
                    let v = Float(value) ?? 0
                    var valueLevel = ""
                    if v > 0 {
                        valueLevel = "Positive"
                    } else {
                        valueLevel = "Negative"
                    }
                    return tips ?? "N/A"//"Based on the results, your LH Ultra level has \(valueLevel)."
                case .conception: return "Please note that the chance of conception varies based on your age and menstrual cycle. It is important to know that this information should not be used as a method of contraception."
                default: return ""
                }
//            }
//            return "N/A"
        }
    }
}


protocol LineChartWithTitleViewDelegate: AnyObject {
    func temperatureSwitch(index: Int)
}

extension LineChartWithTitleViewDelegate {
    func temperatureSwitch(index: Int) {}
}

// with title
class LineChartWithTitleView: UIView {
    let backView: UIView = {
        let v = UIView()
        v.backgroundColor = .white
        return v
    }()
    
    lazy var titleLabel: UILabel = {
        let l = UILabel()
        l.font = .mediumGilroyFont(12)
        l.textColor = .mainTextColor
        l.textAlignment = .left
        l.text = self.chartType.title
        return l
    }()
    
    weak var delegate: LineChartWithTitleViewDelegate?
    lazy var segmentControl: UISegmentedControl = {
        let s = UISegmentedControl(items: ["˚F", "˚C"])
        s.backgroundColor = .themeColor
        s.selectedSegmentTintColor = .mainTextColor
        s.setTitleTextAttributes([NSAttributedString.Key.foregroundColor: UIColor.white], for: .selected)
        s.addTarget(self, action: #selector(segmentValueChanged(_:)), for: .valueChanged)
        s.selectedSegmentIndex = 0
        return s
    }()
    
    var chartType: LineChartDemoView.ChartType = .lhUltra {
        didSet {
            lineChartView.chartType = chartType
            segmentControl.isHidden = chartType != .temp
        }
    }
    var lineChartView = LineChartDemoView(frame: .zero)
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    private func setupUI() {
        layer.cornerRadius = 8
        layer.masksToBounds = true
        addSubview(backView)
        addSubview(lineChartView)
        backView.addSubview(titleLabel)
        backView.addSubview(segmentControl)
        
        backView.snp.makeConstraints { make in
            make.height.equalTo(30)
            make.left.top.right.equalToSuperview()
        }
        
        lineChartView.snp.makeConstraints { make in
            make.top.equalTo(backView.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }
        
        titleLabel.snp.makeConstraints { make in
            make.bottom.equalToSuperview()
            make.left.equalToSuperview().inset(10)
        }
        
        segmentControl.snp.makeConstraints { make in
            make.width.equalTo(80)
            make.height.equalTo(28)
            make.bottom.equalToSuperview()
            make.left.equalToSuperview().inset(20)
        }
    }
    
    @objc func segmentValueChanged(_ sender: UISegmentedControl) {
        let selectedIndex = sender.selectedSegmentIndex
        delegate?.temperatureSwitch(index: selectedIndex)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}
