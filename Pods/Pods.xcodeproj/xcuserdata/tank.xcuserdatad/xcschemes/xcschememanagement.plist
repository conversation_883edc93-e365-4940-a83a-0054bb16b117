<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>SchemeUserState</key>
	<dict>
		<key>Alamofire-Alamofire.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>1</integer>
		</dict>
		<key>Alamofire.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>0</integer>
		</dict>
		<key>AppAuth-AppAuthCore_Privacy.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>35</integer>
		</dict>
		<key>AppAuth.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>53</integer>
		</dict>
		<key>DGCharts-DGCharts.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>3</integer>
		</dict>
		<key>DGCharts.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>2</integer>
		</dict>
		<key>FBAEMKit.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>44</integer>
		</dict>
		<key>FBSDKCoreKit.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>36</integer>
		</dict>
		<key>FBSDKCoreKit_Basics.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>63</integer>
		</dict>
		<key>FBSDKLoginKit.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>42</integer>
		</dict>
		<key>FMDB-FMDB.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>5</integer>
		</dict>
		<key>FMDB.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>4</integer>
		</dict>
		<key>Firebase.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>55</integer>
		</dict>
		<key>FirebaseAnalytics.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>45</integer>
		</dict>
		<key>FirebaseAppCheckInterop.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>40</integer>
		</dict>
		<key>FirebaseAuth-FirebaseAuth_Privacy.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>29</integer>
		</dict>
		<key>FirebaseAuth.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>32</integer>
		</dict>
		<key>FirebaseAuthInterop.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>62</integer>
		</dict>
		<key>FirebaseCore-FirebaseCore_Privacy.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>50</integer>
		</dict>
		<key>FirebaseCore.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>48</integer>
		</dict>
		<key>FirebaseCoreExtension-FirebaseCoreExtension_Privacy.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>26</integer>
		</dict>
		<key>FirebaseCoreExtension.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>47</integer>
		</dict>
		<key>FirebaseCoreInternal-FirebaseCoreInternal_Privacy.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>25</integer>
		</dict>
		<key>FirebaseCoreInternal.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>30</integer>
		</dict>
		<key>FirebaseInstallations-FirebaseInstallations_Privacy.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>33</integer>
		</dict>
		<key>FirebaseInstallations.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>49</integer>
		</dict>
		<key>FirebaseMessaging-FirebaseMessaging_Privacy.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>54</integer>
		</dict>
		<key>FirebaseMessaging.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>59</integer>
		</dict>
		<key>GTMAppAuth-GTMAppAuth_Privacy.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>28</integer>
		</dict>
		<key>GTMAppAuth.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>46</integer>
		</dict>
		<key>GTMSessionFetcher-GTMSessionFetcher_Core_Privacy.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>57</integer>
		</dict>
		<key>GTMSessionFetcher.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>39</integer>
		</dict>
		<key>GoogleAppMeasurement.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>24</integer>
		</dict>
		<key>GoogleDataTransport-GoogleDataTransport_Privacy.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>27</integer>
		</dict>
		<key>GoogleDataTransport.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>56</integer>
		</dict>
		<key>GoogleSignIn-GoogleSignIn.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>31</integer>
		</dict>
		<key>GoogleSignIn.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>37</integer>
		</dict>
		<key>GoogleUtilities-GoogleUtilities_Privacy.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>60</integer>
		</dict>
		<key>GoogleUtilities.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>43</integer>
		</dict>
		<key>HandyJSON.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>61</integer>
		</dict>
		<key>IQKeyboardManagerSwift-IQKeyboardManagerSwift.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>7</integer>
		</dict>
		<key>IQKeyboardManagerSwift.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>6</integer>
		</dict>
		<key>JTAppleCalendar.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>8</integer>
		</dict>
		<key>Kingfisher-Kingfisher.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>58</integer>
		</dict>
		<key>Kingfisher.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>9</integer>
		</dict>
		<key>MBProgressHUD.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>10</integer>
		</dict>
		<key>MJRefresh-MJRefresh.Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>12</integer>
		</dict>
		<key>MJRefresh.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>11</integer>
		</dict>
		<key>ObjectMapper-Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>14</integer>
		</dict>
		<key>ObjectMapper.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>13</integer>
		</dict>
		<key>Pods-HormoneLife.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>15</integer>
		</dict>
		<key>PromisesObjC-FBLPromises_Privacy.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>38</integer>
		</dict>
		<key>PromisesObjC.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>41</integer>
		</dict>
		<key>RecaptchaInterop.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>34</integer>
		</dict>
		<key>SDWebImage-SDWebImage.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>17</integer>
		</dict>
		<key>SDWebImage.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>16</integer>
		</dict>
		<key>SQAutoScrollView.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>20</integer>
		</dict>
		<key>SnapKit-SnapKit_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>19</integer>
		</dict>
		<key>SnapKit.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>18</integer>
		</dict>
		<key>SwiftyJSON-SwiftyJSON.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>22</integer>
		</dict>
		<key>SwiftyJSON.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>21</integer>
		</dict>
		<key>nanopb-nanopb_Privacy.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>52</integer>
		</dict>
		<key>nanopb.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>51</integer>
		</dict>
	</dict>
	<key>SuppressBuildableAutocreation</key>
	<dict/>
</dict>
</plist>
