<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>SchemeUserState</key>
	<dict>
		<key>Alamofire-Alamofire.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Alamofire.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>AppAuth-AppAuthCore_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>AppAuth.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>DGCharts-DGCharts.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>DGCharts.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FBAEMKit.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FBSDKCoreKit.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FBSDKCoreKit_Basics.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FBSDKLoginKit.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FMDB-FMDB.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FMDB.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Firebase.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseAnalytics.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseAppCheckInterop.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseAuth-FirebaseAuth_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseAuth.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseAuthInterop.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseCore-FirebaseCore_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseCore.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseCoreExtension-FirebaseCoreExtension_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseCoreExtension.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseCoreInternal-FirebaseCoreInternal_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseCoreInternal.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseInstallations-FirebaseInstallations_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseInstallations.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseMessaging-FirebaseMessaging_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseMessaging.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GTMAppAuth-GTMAppAuth_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GTMAppAuth.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GTMSessionFetcher-GTMSessionFetcher_Core_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GTMSessionFetcher.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GoogleAppMeasurement.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GoogleDataTransport-GoogleDataTransport_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GoogleDataTransport.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GoogleSignIn-GoogleSignIn.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GoogleSignIn.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GoogleUtilities-GoogleUtilities_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GoogleUtilities.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>HandyJSON.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>IQKeyboardManagerSwift-IQKeyboardManagerSwift.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>IQKeyboardManagerSwift.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>JTAppleCalendar.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Kingfisher-Kingfisher.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Kingfisher.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>MJRefresh-MJRefresh.Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>MJRefresh.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>ObjectMapper-Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>ObjectMapper.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Pods-HormoneLife.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>PromisesObjC-FBLPromises_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>PromisesObjC.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RecaptchaInterop.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>SDWebImage-SDWebImage.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>SDWebImage.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>SnapKit-SnapKit_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>SnapKit.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>SwiftyJSON-SwiftyJSON.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>SwiftyJSON.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>nanopb-nanopb_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>nanopb.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
	</dict>
	<key>SuppressBuildableAutocreation</key>
	<dict/>
</dict>
</plist>
